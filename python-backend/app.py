#!/usr/bin/env python3
"""
NomaToken M-Pesa Backend Service
Flask application providing M-Pesa payment integration for NomaToken platform
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from dotenv import load_dotenv
import structlog

# Load environment variables
load_dotenv()

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['DEBUG'] = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

# CORS configuration
CORS(app, origins=[
    'https://nomatoken.com',
    'https://www.nomatoken.com',
    'http://localhost:3000',  # For development
])

# Rate limiting configuration
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri=os.getenv('REDIS_URL', 'memory://')
)

# Import route modules
from routes.mpesa_auth import mpesa_auth_bp
from routes.mpesa_payment import mpesa_payment_bp
from routes.health import health_bp

# Register blueprints
app.register_blueprint(health_bp)
app.register_blueprint(mpesa_auth_bp, url_prefix='/api/mpesa/auth')
app.register_blueprint(mpesa_payment_bp, url_prefix='/api/mpesa/payment')

@app.before_request
def before_request():
    """Log incoming requests"""
    g.start_time = datetime.utcnow()
    logger.info(
        "request_started",
        method=request.method,
        path=request.path,
        remote_addr=request.remote_addr,
        user_agent=request.headers.get('User-Agent', 'Unknown')
    )

@app.after_request
def after_request(response):
    """Log request completion"""
    duration = datetime.utcnow() - g.start_time
    logger.info(
        "request_completed",
        method=request.method,
        path=request.path,
        status_code=response.status_code,
        duration_ms=duration.total_seconds() * 1000
    )
    return response

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found',
        'message': 'The requested API endpoint does not exist'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error("internal_server_error", error=str(error))
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({
        'success': False,
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please try again later.'
    }), 429

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    host = os.getenv('HOST', '0.0.0.0')
    
    logger.info(
        "starting_server",
        host=host,
        port=port,
        debug=app.config['DEBUG']
    )
    
    app.run(host=host, port=port, debug=app.config['DEBUG'])
