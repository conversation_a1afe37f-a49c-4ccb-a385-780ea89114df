# NomaToken Python Backend - Production Environment
# Copy this file to .env.production and update with your actual values

# Flask Configuration
FLASK_SECRET_KEY=your-super-secret-production-key-here
FLASK_ENV=production
FLASK_DEBUG=False

# M-Pesa Production Configuration
MPESA_ENVIRONMENT=production
MPESA_CONSUMER_KEY=your_production_consumer_key_from_safaricom
MPESA_CONSUMER_SECRET=your_production_consumer_secret_from_safaricom
MPESA_BUSINESS_SHORT_CODE=your_production_business_shortcode
MPESA_PASSKEY=your_production_passkey_from_safaricom

# M-Pesa URLs (Production)
MPESA_CALLBACK_URL=https://nomatoken.com/api/mpesa/payment/callback
MPESA_RESULT_URL=https://nomatoken.com/api/mpesa/payment/result

# CORS Configuration
ALLOWED_ORIGINS=https://nomatoken.com,https://www.nomatoken.com

# Rate Limiting (Redis - if available)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO

# Security
RATE_LIMIT_ENABLED=True
RATE_LIMIT_PER_MINUTE=60

# Application Settings
MIN_PAYMENT_AMOUNT=10
MAX_PAYMENT_AMOUNT=10000
DEFAULT_ACCOUNT_REFERENCE=NomaToken

# Database (if you add one later)
# DATABASE_URL=sqlite:///nomatoken.db
