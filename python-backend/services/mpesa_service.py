"""
M-Pesa Service Module
Handles all M-Pesa API interactions using daraja-mpesa library
"""

import os
import re
import time
import base64
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
import requests
import structlog
import phonenumbers
from phonenumbers import NumberParseException

logger = structlog.get_logger()

class MpesaService:
    """M-Pesa service for handling payments and callbacks"""
    
    def __init__(self):
        self.consumer_key = os.getenv('MPESA_CONSUMER_KEY')
        self.consumer_secret = os.getenv('MPESA_CONSUMER_SECRET')
        self.business_short_code = os.getenv('MPESA_BUSINESS_SHORT_CODE')
        self.passkey = os.getenv('MPESA_PASSKEY')
        self.environment = os.getenv('MPESA_ENVIRONMENT', 'sandbox')
        self.callback_url = os.getenv('MPESA_CALLBACK_URL')
        
        # API URLs
        if self.environment == 'production':
            self.base_url = 'https://api.safaricom.co.ke'
        else:
            self.base_url = 'https://sandbox.safaricom.co.ke'
            
        # Validate configuration
        self._validate_config()
        
        # Rate limiting
        self.rate_limit_requests = {}
        self.max_requests_per_minute = int(os.getenv('MPESA_MAX_REQUESTS_PER_MINUTE', '30'))
        
    def _validate_config(self):
        """Validate M-Pesa configuration"""
        required_vars = [
            'MPESA_CONSUMER_KEY',
            'MPESA_CONSUMER_SECRET', 
            'MPESA_BUSINESS_SHORT_CODE',
            'MPESA_PASSKEY',
            'MPESA_CALLBACK_URL'
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
            
        logger.info("mpesa_config_validated", environment=self.environment)
    
    def _check_rate_limit(self, identifier: str) -> bool:
        """Check if request is within rate limits"""
        current_time = time.time()
        minute_ago = current_time - 60
        
        # Clean old entries
        if identifier in self.rate_limit_requests:
            self.rate_limit_requests[identifier] = [
                req_time for req_time in self.rate_limit_requests[identifier] 
                if req_time > minute_ago
            ]
        else:
            self.rate_limit_requests[identifier] = []
            
        # Check limit
        if len(self.rate_limit_requests[identifier]) >= self.max_requests_per_minute:
            return False
            
        # Add current request
        self.rate_limit_requests[identifier].append(current_time)
        return True
    
    def validate_phone_number(self, phone_number: str) -> Tuple[bool, str]:
        """Validate and format Kenyan phone number"""
        try:
            # Parse the phone number
            parsed = phonenumbers.parse(phone_number, "KE")
            
            # Check if it's valid
            if not phonenumbers.is_valid_number(parsed):
                return False, "Invalid phone number format"
                
            # Format to international format without +
            formatted = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
            formatted = formatted.replace('+', '')
            
            # Ensure it's a Kenyan number
            if not formatted.startswith('254'):
                return False, "Only Kenyan phone numbers are supported"
                
            return True, formatted
            
        except NumberParseException:
            return False, "Invalid phone number format"
    
    def validate_amount(self, amount: float) -> Tuple[bool, str]:
        """Validate payment amount"""
        min_amount = float(os.getenv('MIN_PURCHASE_AMOUNT', '10'))
        max_amount = float(os.getenv('MAX_PURCHASE_AMOUNT', '10000'))
        
        if amount < min_amount:
            return False, f"Amount must be at least ${min_amount}"
            
        if amount > max_amount:
            return False, f"Amount cannot exceed ${max_amount}"
            
        return True, ""
    
    def get_access_token(self) -> Optional[str]:
        """Get M-Pesa access token"""
        try:
            # Create basic auth header
            credentials = f"{self.consumer_key}:{self.consumer_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            
            headers = {
                'Authorization': f'Basic {encoded_credentials}',
                'Content-Type': 'application/json'
            }
            
            url = f"{self.base_url}/oauth/v1/generate?grant_type=client_credentials"
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            access_token = data.get('access_token')
            
            if access_token:
                logger.info("access_token_obtained", expires_in=data.get('expires_in'))
                return access_token
            else:
                logger.error("access_token_missing", response_data=data)
                return None
                
        except requests.RequestException as e:
            logger.error("access_token_request_failed", error=str(e))
            return None
        except Exception as e:
            logger.error("access_token_unexpected_error", error=str(e))
            return None
    
    def generate_password(self, timestamp: str) -> str:
        """Generate M-Pesa password"""
        password_string = f"{self.business_short_code}{self.passkey}{timestamp}"
        return base64.b64encode(password_string.encode()).decode()
    
    def generate_timestamp(self) -> str:
        """Generate timestamp in M-Pesa format"""
        return datetime.now().strftime('%Y%m%d%H%M%S')
    
    def mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Mask sensitive data for logging"""
        masked_data = data.copy()
        sensitive_fields = ['Password', 'PhoneNumber', 'PartyA', 'PartyB']
        
        for field in sensitive_fields:
            if field in masked_data:
                value = str(masked_data[field])
                if len(value) > 4:
                    masked_data[field] = value[:2] + '*' * (len(value) - 4) + value[-2:]
                else:
                    masked_data[field] = '*' * len(value)
                    
        return masked_data

    def initiate_stk_push(self, phone_number: str, amount: float, account_reference: str = "NomaToken") -> Dict[str, Any]:
        """Initiate STK Push payment"""
        try:
            # Check rate limiting
            if not self._check_rate_limit(f"stk_push_{phone_number}"):
                return {
                    'success': False,
                    'error': 'Rate limit exceeded',
                    'message': 'Too many payment requests. Please wait before trying again.'
                }

            # Validate phone number
            is_valid_phone, formatted_phone = self.validate_phone_number(phone_number)
            if not is_valid_phone:
                return {
                    'success': False,
                    'error': 'Invalid phone number',
                    'message': formatted_phone
                }

            # Validate amount
            is_valid_amount, amount_error = self.validate_amount(amount)
            if not is_valid_amount:
                return {
                    'success': False,
                    'error': 'Invalid amount',
                    'message': amount_error
                }

            # Get access token
            access_token = self.get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': 'Authentication failed',
                    'message': 'Failed to authenticate with M-Pesa API'
                }

            # Generate timestamp and password
            timestamp = self.generate_timestamp()
            password = self.generate_password(timestamp)

            # Prepare STK Push payload
            payload = {
                'BusinessShortCode': self.business_short_code,
                'Password': password,
                'Timestamp': timestamp,
                'TransactionType': 'CustomerPayBillOnline',
                'Amount': int(round(amount)),  # M-Pesa requires whole numbers
                'PartyA': formatted_phone,
                'PartyB': self.business_short_code,
                'PhoneNumber': formatted_phone,
                'CallBackURL': self.callback_url,
                'AccountReference': account_reference[:12],  # Max 12 characters
                'TransactionDesc': 'Token Purchase'[:13]  # Max 13 characters
            }

            # Log request (masked)
            logger.info("stk_push_initiated", payload=self.mask_sensitive_data(payload))

            # Make STK Push request
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            url = f"{self.base_url}/mpesa/stkpush/v1/processrequest"

            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()

            response_data = response.json()

            # Log response
            logger.info("stk_push_response", response_data=response_data)

            return {
                'success': True,
                'message': 'STK Push initiated successfully',
                'data': {
                    'MerchantRequestID': response_data.get('MerchantRequestID'),
                    'CheckoutRequestID': response_data.get('CheckoutRequestID'),
                    'ResponseCode': response_data.get('ResponseCode'),
                    'ResponseDescription': response_data.get('ResponseDescription'),
                    'CustomerMessage': response_data.get('CustomerMessage')
                }
            }

        except requests.RequestException as e:
            logger.error("stk_push_request_failed", error=str(e))
            return {
                'success': False,
                'error': 'Request failed',
                'message': 'Failed to communicate with M-Pesa API'
            }
        except Exception as e:
            logger.error("stk_push_unexpected_error", error=str(e))
            return {
                'success': False,
                'error': 'Unexpected error',
                'message': 'An unexpected error occurred'
            }

    def process_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process M-Pesa callback data"""
        try:
            logger.info("callback_received", callback_data=self.mask_sensitive_data(callback_data))

            # Extract callback information
            stk_callback = callback_data.get('Body', {}).get('stkCallback', {})

            if not stk_callback:
                logger.error("invalid_callback_format", callback_data=callback_data)
                return {
                    'success': False,
                    'error': 'Invalid callback format'
                }

            result_code = stk_callback.get('ResultCode')
            merchant_request_id = stk_callback.get('MerchantRequestID')
            checkout_request_id = stk_callback.get('CheckoutRequestID')
            result_desc = stk_callback.get('ResultDesc')

            if result_code == 0:
                # Payment successful
                callback_metadata = stk_callback.get('CallbackMetadata', {}).get('Item', [])
                metadata = self._extract_callback_metadata(callback_metadata)

                payment_data = {
                    'merchantRequestId': merchant_request_id,
                    'checkoutRequestId': checkout_request_id,
                    'amount': metadata.get('Amount'),
                    'mpesaReceiptNumber': metadata.get('MpesaReceiptNumber'),
                    'transactionDate': metadata.get('TransactionDate'),
                    'phoneNumber': metadata.get('PhoneNumber'),
                    'resultCode': result_code,
                    'resultDesc': result_desc,
                    'status': 'completed',
                    'timestamp': datetime.utcnow().isoformat()
                }

                logger.info("payment_successful", payment_data=self.mask_sensitive_data(payment_data))

                # TODO: Implement database storage and token allocation
                # This is where you would:
                # 1. Store payment in database
                # 2. Calculate token amount
                # 3. Update user balance
                # 4. Send confirmation

                return {
                    'success': True,
                    'status': 'completed',
                    'data': payment_data
                }
            else:
                # Payment failed
                logger.info("payment_failed",
                           merchant_request_id=merchant_request_id,
                           checkout_request_id=checkout_request_id,
                           result_code=result_code,
                           result_desc=result_desc)

                return {
                    'success': True,
                    'status': 'failed',
                    'data': {
                        'merchantRequestId': merchant_request_id,
                        'checkoutRequestId': checkout_request_id,
                        'resultCode': result_code,
                        'resultDesc': result_desc,
                        'status': 'failed',
                        'timestamp': datetime.utcnow().isoformat()
                    }
                }

        except Exception as e:
            logger.error("callback_processing_error", error=str(e))
            return {
                'success': False,
                'error': 'Callback processing failed'
            }

    def _extract_callback_metadata(self, metadata_items: list) -> Dict[str, Any]:
        """Extract metadata from callback items"""
        metadata = {}

        for item in metadata_items:
            name = item.get('Name')
            value = item.get('Value')

            if name and value is not None:
                metadata[name] = value

        return metadata

# Create global instance
mpesa_service = MpesaService()
