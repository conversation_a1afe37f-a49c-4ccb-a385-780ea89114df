#!/usr/bin/env python3
"""
WSGI entry point for NomaToken M-Pesa Backend
This file is used by CPanel and other WSGI servers to run the application
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

# Load environment variables
load_dotenv('.env.production')

# Import the Flask application
from app import app

# WSGI application
application = app

if __name__ == "__main__":
    # For local testing
    app.run(debug=False)
