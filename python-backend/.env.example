# NomaToken Python M-Pesa Backend Environment Variables
# Copy this file to .env and update with your actual values

# Flask Configuration
FLASK_SECRET_KEY=your-secret-key-change-in-production
FLASK_DEBUG=False
HOST=0.0.0.0
PORT=5000

# M-Pesa Configuration
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_BUSINESS_SHORT_CODE=174379
MPESA_PASSKEY=bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
MPESA_CALLBACK_URL=https://nomatoken.com/api/mpesa/payment/callback

# M-Pesa Rate Limiting
MPESA_MAX_REQUESTS_PER_MINUTE=30
MPESA_MAX_PAYMENT_ATTEMPTS=5
MPESA_PAYMENT_TIMEOUT_MS=120000

# Token Configuration
MIN_PURCHASE_AMOUNT=10
MAX_PURCHASE_AMOUNT=10000
NOMA_TOKEN_PRICE=0.0245

# Redis Configuration (for rate limiting)
REDIS_URL=redis://localhost:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Configuration
ALLOWED_ORIGINS=https://nomatoken.com,https://www.nomatoken.com

# Database Configuration (when implemented)
# DATABASE_URL=postgresql://user:password@localhost/nomatoken
# DATABASE_POOL_SIZE=10

# Security
RATE_LIMIT_STORAGE=memory://
RATE_LIMIT_PER_METHOD=True

# Monitoring (optional)
SENTRY_DSN=your_sentry_dsn_here
ENABLE_METRICS=False
