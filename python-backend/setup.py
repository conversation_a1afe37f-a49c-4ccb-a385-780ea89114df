#!/usr/bin/env python3
"""
Setup script for NomaToken Python M-Pesa Backend
Helps with initial configuration and testing
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def check_environment_file():
    """Check if environment file exists"""
    env_file = Path(".env.production")
    if env_file.exists():
        print("✅ Environment file found")
        return True
    else:
        print("⚠️  Environment file not found")
        print("Please create .env.production with your M-Pesa credentials")
        return False

def test_flask_app():
    """Test if Flask app can start"""
    print("🧪 Testing Flask application...")
    try:
        from app import app
        print("✅ Flask app imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import Flask app: {e}")
        return False

def test_mpesa_service():
    """Test M-Pesa service configuration"""
    print("🧪 Testing M-Pesa service...")
    try:
        from services.mpesa_service import mpesa_service
        print("✅ M-Pesa service imported successfully")
        return True
    except Exception as e:
        print(f"❌ M-Pesa service error: {e}")
        return False

def run_health_check():
    """Run a basic health check"""
    print("🏥 Running health check...")
    try:
        from app import app
        with app.test_client() as client:
            response = client.get('/health')
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 NomaToken Python Backend Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Check environment
    env_ok = check_environment_file()
    
    # Test Flask app
    if not test_flask_app():
        sys.exit(1)
    
    # Test M-Pesa service (only if env is OK)
    if env_ok:
        test_mpesa_service()
        run_health_check()
    
    print("\n" + "=" * 40)
    print("🎉 Setup completed!")
    
    if env_ok:
        print("\n📋 Next steps:")
        print("1. Deploy to CPanel Python App")
        print("2. Set application URL to /api")
        print("3. Set startup file to wsgi.py")
        print("4. Test endpoints:")
        print("   - https://yourdomain.com/api/health")
        print("   - https://yourdomain.com/api/mpesa/auth/validate")
    else:
        print("\n⚠️  Please create .env.production before deployment")
        print("Copy .env.example and update with your credentials")

if __name__ == "__main__":
    main()
