"""
M-Pesa Payment Routes
Handles payment initiation, callbacks, and status checking
"""

from flask import Blueprint, request, jsonify
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from marshmallow import Schema, fields, ValidationError
import structlog

from services.mpesa_service import mpesa_service

logger = structlog.get_logger()

mpesa_payment_bp = Blueprint('mpesa_payment', __name__)

# Request validation schemas
class PaymentInitiateSchema(Schema):
    phoneNumber = fields.Str(required=True)
    amount = fields.Float(required=True, validate=lambda x: x > 0)
    accountReference = fields.Str(missing="NomaToken", validate=lambda x: len(x) <= 12)

class PaymentStatusSchema(Schema):
    checkoutRequestId = fields.Str(required=True)
    merchantRequestId = fields.Str(missing=None)

# Initialize schemas
payment_initiate_schema = PaymentInitiateSchema()
payment_status_schema = PaymentStatusSchema()

@mpesa_payment_bp.route('/initiate', methods=['POST'])
def initiate_payment():
    """Initiate M-Pesa STK Push payment"""
    try:
        # Validate request data
        try:
            data = payment_initiate_schema.load(request.get_json() or {})
        except ValidationError as err:
            logger.warning("payment_initiate_validation_error", errors=err.messages)
            return jsonify({
                'success': False,
                'error': 'Validation error',
                'message': 'Invalid request data',
                'details': err.messages
            }), 400
        
        logger.info("payment_initiate_request", 
                   phone_number=data['phoneNumber'][:3] + '***' + data['phoneNumber'][-3:],
                   amount=data['amount'],
                   account_reference=data['accountReference'])
        
        # Initiate STK Push
        result = mpesa_service.initiate_stk_push(
            phone_number=data['phoneNumber'],
            amount=data['amount'],
            account_reference=data['accountReference']
        )
        
        if result['success']:
            return jsonify(result)
        else:
            status_code = 400 if result.get('error') in ['Invalid phone number', 'Invalid amount', 'Rate limit exceeded'] else 500
            return jsonify(result), status_code
            
    except Exception as e:
        logger.error("payment_initiate_error", error=str(e))
        return jsonify({
            'success': False,
            'error': 'Payment initiation failed',
            'message': 'An unexpected error occurred'
        }), 500

@mpesa_payment_bp.route('/callback', methods=['POST'])
def payment_callback():
    """Handle M-Pesa payment callback"""
    try:
        callback_data = request.get_json()
        
        if not callback_data:
            logger.error("callback_no_data")
            return jsonify({
                'success': True,
                'message': 'No callback data received'
            })
        
        logger.info("callback_received", 
                   remote_addr=request.remote_addr,
                   has_data=bool(callback_data))
        
        # Process the callback
        result = mpesa_service.process_callback(callback_data)
        
        # Always return success to M-Pesa to avoid retries
        return jsonify({
            'success': True,
            'message': 'Callback processed successfully'
        })
        
    except Exception as e:
        logger.error("callback_processing_error", error=str(e))
        # Still return success to avoid M-Pesa retries
        return jsonify({
            'success': True,
            'message': 'Callback received'
        })

@mpesa_payment_bp.route('/callback', methods=['GET'])
def callback_test():
    """Test endpoint for callback URL verification"""
    return jsonify({
        'message': 'M-Pesa callback endpoint is active',
        'timestamp': mpesa_service.generate_timestamp(),
        'service': 'NomaToken M-Pesa Backend'
    })

@mpesa_payment_bp.route('/status', methods=['GET'])
def check_payment_status():
    """Check payment status by checkout request ID"""
    try:
        # Get query parameters
        checkout_request_id = request.args.get('checkoutRequestId')
        merchant_request_id = request.args.get('merchantRequestId')
        
        if not checkout_request_id:
            return jsonify({
                'success': False,
                'error': 'Missing parameter',
                'message': 'checkoutRequestId is required'
            }), 400
        
        logger.info("payment_status_check", 
                   checkout_request_id=checkout_request_id,
                   merchant_request_id=merchant_request_id)
        
        # TODO: Implement database lookup for payment status
        # For now, return a placeholder response
        return jsonify({
            'success': True,
            'message': 'Payment status check not yet implemented',
            'data': {
                'checkoutRequestId': checkout_request_id,
                'merchantRequestId': merchant_request_id,
                'status': 'pending',
                'message': 'Database integration pending'
            }
        })
        
    except Exception as e:
        logger.error("payment_status_error", error=str(e))
        return jsonify({
            'success': False,
            'error': 'Status check failed',
            'message': 'An unexpected error occurred'
        }), 500

@mpesa_payment_bp.route('/status', methods=['POST'])
def update_payment_status():
    """Update payment status (for internal use)"""
    try:
        # Validate request data
        try:
            data = payment_status_schema.load(request.get_json() or {})
        except ValidationError as err:
            return jsonify({
                'success': False,
                'error': 'Validation error',
                'message': 'Invalid request data',
                'details': err.messages
            }), 400
        
        logger.info("payment_status_update", 
                   checkout_request_id=data['checkoutRequestId'],
                   merchant_request_id=data.get('merchantRequestId'))
        
        # TODO: Implement database update for payment status
        return jsonify({
            'success': True,
            'message': 'Payment status update not yet implemented',
            'data': {
                'checkoutRequestId': data['checkoutRequestId'],
                'merchantRequestId': data.get('merchantRequestId'),
                'message': 'Database integration pending'
            }
        })
        
    except Exception as e:
        logger.error("payment_status_update_error", error=str(e))
        return jsonify({
            'success': False,
            'error': 'Status update failed',
            'message': 'An unexpected error occurred'
        }), 500
