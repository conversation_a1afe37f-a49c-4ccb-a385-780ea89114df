"""
Health Check Routes
Provides health and status endpoints for the M-Pesa backend service
"""

import os
from datetime import datetime
from flask import Blueprint, jsonify
import structlog

logger = structlog.get_logger()

health_bp = Blueprint('health', __name__)

@health_bp.route('/health', methods=['GET'])
def health_check():
    """Basic health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'NomaToken M-Pesa Backend',
        'version': '1.0.0',
        'timestamp': datetime.utcnow().isoformat(),
        'environment': os.getenv('MPESA_ENVIRONMENT', 'sandbox')
    })

@health_bp.route('/api/health', methods=['GET'])
def api_health_check():
    """API health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'NomaToken M-Pesa API',
        'version': '1.0.0',
        'timestamp': datetime.utcnow().isoformat(),
        'endpoints': {
            'auth': '/api/mpesa/auth/token',
            'payment_initiate': '/api/mpesa/payment/initiate',
            'payment_callback': '/api/mpesa/payment/callback',
            'payment_status': '/api/mpesa/payment/status'
        }
    })

@health_bp.route('/status', methods=['GET'])
def status_check():
    """Detailed status check"""
    try:
        # Check M-Pesa configuration
        required_env_vars = [
            'MPESA_CONSUMER_KEY',
            'MPESA_CONSUMER_SECRET',
            'MPESA_BUSINESS_SHORT_CODE',
            'MPESA_PASSKEY',
            'MPESA_CALLBACK_URL'
        ]
        
        config_status = {}
        for var in required_env_vars:
            config_status[var] = 'configured' if os.getenv(var) else 'missing'
        
        all_configured = all(os.getenv(var) for var in required_env_vars)
        
        return jsonify({
            'status': 'healthy' if all_configured else 'degraded',
            'service': 'NomaToken M-Pesa Backend',
            'timestamp': datetime.utcnow().isoformat(),
            'configuration': {
                'mpesa_environment': os.getenv('MPESA_ENVIRONMENT', 'sandbox'),
                'variables': config_status,
                'all_configured': all_configured
            },
            'features': {
                'stk_push': all_configured,
                'callback_processing': all_configured,
                'rate_limiting': True,
                'logging': True
            }
        })
        
    except Exception as e:
        logger.error("status_check_error", error=str(e))
        return jsonify({
            'status': 'unhealthy',
            'service': 'NomaToken M-Pesa Backend',
            'timestamp': datetime.utcnow().isoformat(),
            'error': 'Status check failed'
        }), 500
