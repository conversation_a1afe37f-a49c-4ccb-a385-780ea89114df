"""
M-Pesa Authentication Routes
Handles M-Pesa API authentication and token generation
"""

from flask import Blueprint, jsonify, request
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import structlog

from services.mpesa_service import mpesa_service

logger = structlog.get_logger()

mpesa_auth_bp = Blueprint('mpesa_auth', __name__)

@mpesa_auth_bp.route('/token', methods=['POST'])
def generate_token():
    """Generate M-Pesa access token"""
    try:
        logger.info("token_request_received", 
                   remote_addr=request.remote_addr,
                   user_agent=request.headers.get('User-Agent'))
        
        # Get access token from M-Pesa service
        access_token = mpesa_service.get_access_token()
        
        if access_token:
            return jsonify({
                'success': True,
                'access_token': access_token,
                'expires_in': 3599,  # M-Pesa tokens typically expire in 1 hour
                'token_type': 'Bearer'
            })
        else:
            logger.error("token_generation_failed")
            return jsonify({
                'success': False,
                'error': 'Failed to generate access token',
                'message': 'Unable to authenticate with M-Pesa API'
            }), 500
            
    except Exception as e:
        logger.error("token_endpoint_error", error=str(e))
        return jsonify({
            'success': False,
            'error': 'Token generation failed',
            'message': 'An unexpected error occurred'
        }), 500

@mpesa_auth_bp.route('/validate', methods=['POST'])
def validate_credentials():
    """Validate M-Pesa credentials (for testing)"""
    try:
        logger.info("credential_validation_requested",
                   remote_addr=request.remote_addr)
        
        # Attempt to get access token to validate credentials
        access_token = mpesa_service.get_access_token()
        
        if access_token:
            return jsonify({
                'success': True,
                'message': 'M-Pesa credentials are valid',
                'environment': mpesa_service.environment,
                'business_short_code': mpesa_service.business_short_code
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid credentials',
                'message': 'M-Pesa credentials validation failed'
            }), 401
            
    except Exception as e:
        logger.error("credential_validation_error", error=str(e))
        return jsonify({
            'success': False,
            'error': 'Validation failed',
            'message': 'An unexpected error occurred during validation'
        }), 500
