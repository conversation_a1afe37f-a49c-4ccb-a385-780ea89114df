/**
 * API Configuration for NomaToken Platform
 * Manages API endpoints for static frontend and Python backend integration
 */

export const API_CONFIG = {
  // Static frontend API (for non-M-Pesa operations)
  FRONTEND_API_URL: process.env.NEXT_PUBLIC_API_URL || '/api',
  
  // Python backend API (for M-Pesa operations)
  PYTHON_API_URL: process.env.NEXT_PUBLIC_PYTHON_API_URL || 'https://nomatoken.com/api',
  
  // External APIs
  COINGECKO_API: process.env.NEXT_PUBLIC_COINGECKO_API || 'https://api.coingecko.com/api/v3',
  
  // Site configuration
  SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://nomatoken.com',
  
  // Feature flags
  ENABLE_ANIMATIONS: process.env.NEXT_PUBLIC_ENABLE_ANIMATIONS === 'true',
  ENABLE_WALLET_CONNECT: process.env.NEXT_PUBLIC_ENABLE_WALLET_CONNECT === 'true',
  ENABLE_STAKING: process.env.NEXT_PUBLIC_ENABLE_STAKING === 'true',
  ENABLE_PRESALE: process.env.NEXT_PUBLIC_ENABLE_PRESALE === 'true',
  
  // M-Pesa configuration
  MPESA: {
    MIN_AMOUNT: parseFloat(process.env.NEXT_PUBLIC_MIN_PURCHASE_AMOUNT || '10'),
    MAX_AMOUNT: parseFloat(process.env.NEXT_PUBLIC_MAX_PURCHASE_AMOUNT || '10000'),
    TIMEOUT_MS: 120000, // 2 minutes
    STATUS_CHECK_INTERVAL_MS: 3000, // 3 seconds
  },
  
  // Token configuration
  TOKEN: {
    PRICE: parseFloat(process.env.NOMA_TOKEN_PRICE || '0.0245'),
    MIN_PURCHASE: parseFloat(process.env.MIN_PURCHASE_AMOUNT || '10'),
    MAX_PURCHASE: parseFloat(process.env.MAX_PURCHASE_AMOUNT || '10000'),
  },
  
  // Request timeouts
  TIMEOUTS: {
    PAYMENT_INITIATION: 30000, // 30 seconds
    STATUS_CHECK: 10000, // 10 seconds
    TOKEN_PURCHASE: 30000, // 30 seconds
    DEFAULT: 15000, // 15 seconds
  },
  
  // Rate limiting
  RATE_LIMITS: {
    PAYMENT_REQUESTS_PER_MINUTE: 5,
    STATUS_CHECKS_PER_MINUTE: 20,
    TOKEN_PURCHASES_PER_MINUTE: 3,
  }
};

/**
 * Get the appropriate API URL for different operations
 */
export const getApiUrl = (operation: 'mpesa' | 'tokens' | 'general' = 'general'): string => {
  switch (operation) {
    case 'mpesa':
      return API_CONFIG.PYTHON_API_URL;
    case 'tokens':
    case 'general':
    default:
      return API_CONFIG.FRONTEND_API_URL;
  }
};

/**
 * Check if we're in development mode
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * Check if we're in production mode
 */
export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

/**
 * Get CORS origins for API requests
 */
export const getCorsOrigins = (): string[] => {
  const origins = [API_CONFIG.SITE_URL];
  
  if (isDevelopment()) {
    origins.push('http://localhost:3000', 'http://127.0.0.1:3000');
  }
  
  return origins;
};
