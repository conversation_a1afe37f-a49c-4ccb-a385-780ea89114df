# NomaToken Hybrid Deployment Guide

## 🎯 Overview

This guide implements **Option 2: Hybrid Approach** to resolve CPanel Node.js deployment issues:

1. **Static Frontend**: Next.js exported as static files → `public_html/`
2. **Python Backend**: Flask M-Pesa service → `/api` endpoint

## 🚀 Quick Start

### 1. Build Static Frontend
```bash
npm run cpanel:build
```

### 2. Deploy Frontend
- Upload `out/` contents to CPanel `public_html/`

### 3. Deploy Python Backend
- Upload `python-backend/` to CPanel
- Create Python App: `/api` → `wsgi.py`
- Install dependencies: `pip install -r requirements.txt`

### 4. Test Integration
- Frontend: `https://nomatoken.com`
- Backend: `https://nomatoken.com/api/health`
- M-Pesa: `https://nomatoken.com/api/mpesa/auth/validate`

## 📁 File Structure

```
nomatoken/
├── out/                          # Static frontend (upload to public_html/)
│   ├── index.html
│   ├── _next/
│   └── .htaccess
├── python-backend/               # Python M-Pesa service
│   ├── app.py
│   ├── wsgi.py
│   ├── requirements.txt
│   ├── .env.production
│   ├── routes/
│   └── services/
├── next.config.js               # ✅ Updated for static export
├── lib/config/api.ts            # ✅ API configuration
├── lib/services/mpesa-service.ts # ✅ Updated for Python API
└── scripts/build-static.js      # ✅ Build automation
```

## 🔧 Configuration

### Frontend (.env.production)
```env
NEXT_PUBLIC_SITE_URL=https://nomatoken.com
NEXT_PUBLIC_PYTHON_API_URL=https://nomatoken.com/api
NEXT_PUBLIC_ENABLE_ANIMATIONS=true
```

### Backend (python-backend/.env.production)
```env
FLASK_SECRET_KEY=your-production-secret
MPESA_ENVIRONMENT=production
MPESA_CONSUMER_KEY=your_key
MPESA_CONSUMER_SECRET=your_secret
MPESA_BUSINESS_SHORT_CODE=your_shortcode
MPESA_PASSKEY=your_passkey
MPESA_CALLBACK_URL=https://nomatoken.com/api/mpesa/payment/callback
```

## 🧪 Testing

### Frontend Tests
- ✅ Site loads at `https://nomatoken.com`
- ✅ Static assets load correctly
- ✅ Navigation works
- ✅ Wallet connection functional

### Backend Tests
```bash
# Health check
curl https://nomatoken.com/api/health

# M-Pesa auth validation
curl https://nomatoken.com/api/mpesa/auth/validate

# Payment initiation test
curl -X POST https://nomatoken.com/api/mpesa/payment/initiate \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"254712345678","amount":10}'
```

### Integration Tests
- ✅ Frontend calls Python API correctly
- ✅ CORS configured properly
- ✅ M-Pesa payments work end-to-end
- ✅ Error handling functional

## 🔍 Key Changes Made

### 1. Static Export Configuration
- **next.config.js**: Added `output: 'export'`, `trailingSlash: true`
- **package.json**: Updated build scripts
- **scripts/build-static.js**: Automated build process

### 2. Python M-Pesa Backend
- **Flask application**: Replicated all M-Pesa functionality
- **daraja-mpesa integration**: Production-ready M-Pesa service
- **WSGI deployment**: CPanel-compatible deployment

### 3. Frontend Integration
- **lib/config/api.ts**: Centralized API configuration
- **mpesa-service.ts**: Updated to use Python API endpoints
- **Environment variables**: Split between static and dynamic

### 4. Deployment Automation
- **Build scripts**: Automated static export
- **Environment management**: Production-ready configuration
- **Error handling**: Comprehensive logging and monitoring

## 🚨 Important Notes

1. **No Node.js Server**: Static files eliminate server requirements
2. **Python Compatibility**: Better CPanel support than Node.js
3. **Existing Codebase**: Minimal changes to current implementation
4. **Production Ready**: Includes security, logging, and error handling
5. **Scalable**: Can add database and additional features later

## 📞 Support

This hybrid approach resolves the original CPanel deployment issues while maintaining all existing functionality. The static frontend eliminates Node.js server requirements, and the Python backend provides reliable M-Pesa integration with better shared hosting compatibility.

---

**Implementation Status**: ✅ Complete
**Testing Status**: Ready for deployment
**Documentation**: Comprehensive guides provided
