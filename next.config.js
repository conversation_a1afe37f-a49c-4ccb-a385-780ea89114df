/** @type {import('next').NextConfig} */
const nextConfig = {
  // Static export configuration for CPanel deployment
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  distDir: 'out',

  eslint: {
    ignoreDuringBuilds: true,
  },

  images: {
    unoptimized: true, // Required for static export
    domains: ['localhost', 'nomatoken.com'],
  },

  // Environment variables that should be available at build time
  env: {
    NEXT_PUBLIC_PYTHON_API_URL: process.env.NEXT_PUBLIC_PYTHON_API_URL || 'https://nomatoken.com/api',
  },

  // Disable features that don't work with static export
  typescript: {
    ignoreBuildErrors: false, // Keep type checking for development
  },
};

module.exports = nextConfig;
