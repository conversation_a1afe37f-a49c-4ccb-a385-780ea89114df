# 📦 Compressing Out Folder for CPanel Upload

## 🚀 Quick Commands

### Windows (PowerShell)
```powershell
# After running npm run cpanel:build
Compress-Archive -Path "out\*" -DestinationPath "nomatoken-static.zip" -Force
```

### macOS/Linux (Terminal)
```bash
# After running npm run cpanel:build
cd out
zip -r ../nomatoken-static.zip .
cd ..
```

### Alternative (Cross-platform)
```bash
# Using tar (creates smaller files)
tar -czf nomatoken-static.tar.gz -C out .
```

## 🖱️ GUI Methods

### Windows
1. Right-click the `out` folder
2. Select "Send to" → "Compressed (zipped) folder"
3. Rename to `nomatoken-static.zip`

### macOS
1. Right-click the `out` folder
2. Select "Compress 'out'"
3. Rename to `nomatoken-static.zip`

### Linux (Ubuntu/GNOME)
1. Right-click the `out` folder
2. Select "Compress..."
3. Choose ZIP format
4. Name it `nomatoken-static.zip`

## 📤 CPanel Upload Process

### Method 1: Upload Compressed File (Recommended)
1. **Build**: Run `npm run cpanel:build`
2. **Compress**: Use any method above to create `nomatoken-static.zip`
3. **Upload**: 
   - Go to CPanel → File Manager
   - Navigate to `public_html/`
   - Upload `nomatoken-static.zip`
4. **Extract**:
   - Right-click the uploaded zip file
   - Select "Extract"
   - Choose "Extract to current directory"
5. **Cleanup**: Delete the zip file after extraction

### Method 2: Direct Upload (Slower)
1. **Build**: Run `npm run cpanel:build`
2. **Upload**:
   - Go to CPanel → File Manager
   - Navigate to `public_html/`
   - Select all files from `out/` folder
   - Upload (may take longer for many files)

## 🔧 Automated Build with Compression

Your build script now includes automatic compression:

```bash
npm run cpanel:build
```

This will:
1. ✅ Build the static export
2. ✅ Generate `.htaccess` file
3. ✅ Create `nomatoken-static.zip` automatically
4. ✅ Provide upload instructions

## 📊 File Size Comparison

| Method | Typical Size | Upload Time |
|--------|-------------|-------------|
| Individual files | ~50MB | 5-10 minutes |
| ZIP compression | ~15MB | 1-2 minutes |
| TAR.GZ compression | ~12MB | 1-2 minutes |

## 🚨 Important Notes

1. **Always extract in `public_html/`**: Don't leave the zip file in the web directory
2. **Check file permissions**: Ensure files are 644 and directories are 755
3. **Verify `.htaccess`**: Make sure it's uploaded and not hidden
4. **Test after upload**: Visit your domain to confirm everything works

## 🔍 Troubleshooting

### Compression Issues
- **Command not found**: Use GUI method instead
- **Permission denied**: Run terminal as administrator
- **File too large**: Check if you have enough disk space

### Upload Issues
- **Upload fails**: Try smaller chunks or use compression
- **Files missing**: Ensure you extracted the zip file
- **Site not working**: Check file permissions and `.htaccess`

## 💡 Pro Tips

1. **Use compression**: Always compress for faster uploads
2. **Keep backups**: Save the zip file as a backup
3. **Test locally**: Verify the build works before uploading
4. **Monitor size**: Large files may hit hosting limits

---

**Recommended workflow**: `npm run cpanel:build` → Upload `nomatoken-static.zip` → Extract in CPanel → Test site
