#!/usr/bin/env node

/**
 * Static Build Script for NomaToken CPanel Deployment
 * This script prepares the Next.js application for static export
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to create compressed archive
function createCompressedArchive() {
  return new Promise((resolve, reject) => {
    console.log('📦 Creating compressed archive...');

    try {
      // Use native zip command if available, otherwise use Node.js approach
      if (process.platform === 'win32') {
        // Windows PowerShell
        execSync('powershell Compress-Archive -Path "out\\*" -DestinationPath "nomatoken-static.zip" -Force', { stdio: 'inherit' });
      } else {
        // macOS/Linux
        execSync('cd out && zip -r ../nomatoken-static.zip . && cd ..', { stdio: 'inherit' });
      }
      console.log('✅ Archive created: nomatoken-static.zip');
      resolve();
    } catch (error) {
      console.log('⚠️  Native zip not available, creating manual archive...');
      // Fallback: create a simple archive instruction
      const instructions = `
# Manual Compression Instructions

## Option 1: Command Line
cd out
zip -r ../nomatoken-static.zip .

## Option 2: GUI
1. Right-click the 'out' folder
2. Select "Compress" or "Send to > Compressed folder"
3. Rename to "nomatoken-static.zip"

## Option 3: Online Tools
Upload the 'out' folder contents to an online zip creator
`;
      fs.writeFileSync('COMPRESSION_INSTRUCTIONS.txt', instructions);
      console.log('📝 Created COMPRESSION_INSTRUCTIONS.txt');
      resolve();
    }
  });
}

console.log('🚀 Starting NomaToken static build process...');

// Check if production environment file exists
const prodEnvPath = path.join(process.cwd(), '.env.production');
if (!fs.existsSync(prodEnvPath)) {
  console.error('❌ .env.production file not found!');
  console.log('Please create .env.production with your production environment variables.');
  process.exit(1);
}

// Load production environment variables
require('dotenv').config({ path: prodEnvPath });

console.log('✅ Production environment loaded');

(async () => {
try {
  // Clean previous build
  console.log('🧹 Cleaning previous build...');
  if (fs.existsSync('out')) {
    fs.rmSync('out', { recursive: true, force: true });
  }
  if (fs.existsSync('.next')) {
    fs.rmSync('.next', { recursive: true, force: true });
  }

  // Build the application
  console.log('🔨 Building Next.js application...');
  execSync('npm run build:static', { stdio: 'inherit' });

  // Verify build output
  const outDir = path.join(process.cwd(), 'out');
  if (!fs.existsSync(outDir)) {
    throw new Error('Build output directory not found');
  }

  // Check for essential files
  const essentialFiles = ['index.html', '_next'];
  for (const file of essentialFiles) {
    const filePath = path.join(outDir, file);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Essential file/directory missing: ${file}`);
    }
  }

  // Create .htaccess for CPanel
  const htaccessContent = `# NomaToken Static Site Configuration
RewriteEngine On

# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache static assets
<FilesMatch "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
  ExpiresActive On
  ExpiresDefault "access plus 1 year"
</FilesMatch>

# Compress files
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>`;

  fs.writeFileSync(path.join(outDir, '.htaccess'), htaccessContent);

  // Create compressed archive for easy upload
  await createCompressedArchive();

  console.log('✅ Static build completed successfully!');
  console.log('📁 Build output available in: ./out/');
  console.log('📦 Compressed archive: ./nomatoken-static.zip');
  console.log('📋 Next steps:');
  console.log('   1. Upload nomatoken-static.zip to CPanel File Manager');
  console.log('   2. Extract the zip file in public_html directory');
  console.log('   3. Deploy the Python M-Pesa backend service');
  console.log('   4. Test the complete application');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
})();
