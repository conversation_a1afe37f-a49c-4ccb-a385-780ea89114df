#!/usr/bin/env node

/**
 * Static Build Script for NomaToken CPanel Deployment
 * This script prepares the Next.js application for static export
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting NomaToken static build process...');

// Check if production environment file exists
const prodEnvPath = path.join(process.cwd(), '.env.production');
if (!fs.existsSync(prodEnvPath)) {
  console.error('❌ .env.production file not found!');
  console.log('Please create .env.production with your production environment variables.');
  process.exit(1);
}

// Load production environment variables
require('dotenv').config({ path: prodEnvPath });

console.log('✅ Production environment loaded');

try {
  // Clean previous build
  console.log('🧹 Cleaning previous build...');
  if (fs.existsSync('out')) {
    fs.rmSync('out', { recursive: true, force: true });
  }
  if (fs.existsSync('.next')) {
    fs.rmSync('.next', { recursive: true, force: true });
  }

  // Build the application
  console.log('🔨 Building Next.js application...');
  execSync('npm run build:static', { stdio: 'inherit' });

  // Verify build output
  const outDir = path.join(process.cwd(), 'out');
  if (!fs.existsSync(outDir)) {
    throw new Error('Build output directory not found');
  }

  // Check for essential files
  const essentialFiles = ['index.html', '_next'];
  for (const file of essentialFiles) {
    const filePath = path.join(outDir, file);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Essential file/directory missing: ${file}`);
    }
  }

  // Create .htaccess for CPanel
  const htaccessContent = `# NomaToken Static Site Configuration
RewriteEngine On

# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache static assets
<FilesMatch "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
  ExpiresActive On
  ExpiresDefault "access plus 1 year"
</FilesMatch>

# Compress files
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>`;

  fs.writeFileSync(path.join(outDir, '.htaccess'), htaccessContent);

  console.log('✅ Static build completed successfully!');
  console.log('📁 Build output available in: ./out/');
  console.log('📋 Next steps:');
  console.log('   1. Upload contents of ./out/ to your CPanel public_html directory');
  console.log('   2. Deploy the Python M-Pesa backend service');
  console.log('   3. Test the complete application');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
